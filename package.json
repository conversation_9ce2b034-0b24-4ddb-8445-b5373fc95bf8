{"name": "klicktape-socket-server", "version": "1.0.0", "description": "Socket.IO server for Klicktape real-time chat", "main": "socket-server.js", "scripts": {"start": "node socket-server.js", "dev": "nodemon socket-server.js", "build": "echo 'No build step required'", "test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "socket.io": "^4.8.1", "@supabase/supabase-js": "^2.39.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["socket.io", "real-time", "chat", "websocket"], "author": "Klicktape", "license": "MIT"}